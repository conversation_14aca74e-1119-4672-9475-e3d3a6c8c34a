const crypto = require('crypto');

/**
 * 抖音密钥生成器
 * 基于对抖音 req.js 的深度分析实现
 * 支持完整的 ECDH + HKDF + HMAC 签名流程
 */
class DouyinKeyGenerator {

    /**
     * HKDF (HMAC-based Key Derivation Function) 实现
     * 基于 RFC 5869 标准，与抖音 req.js 中的实现保持一致
     */
    static hkdfExtract(salt, inputKeyMaterial) {
        if (salt.length === 0) {
            salt = Buffer.alloc(32, 0);
        }
        return crypto.createHmac('sha256', salt).update(inputKeyMaterial).digest();
    }

    static hkdfExpand(pseudoRandomKey, info, length) {
        const hashLength = 32;
        const n = Math.ceil(length / hashLength);
        let okm = Buffer.alloc(0);
        let t = Buffer.alloc(0);

        for (let i = 1; i <= n; i++) {
            const hmac = crypto.createHmac('sha256', pseudoRandomKey);
            hmac.update(t);
            hmac.update(info);
            hmac.update(Buffer.from([i]));
            t = hmac.digest();
            okm = Buffer.concat([okm, t]);
        }

        return okm.slice(0, length);
    }

    static hkdf(salt, inputKeyMaterial, info, length) {
        const prk = this.hkdfExtract(salt, inputKeyMaterial);
        return this.hkdfExpand(prk, info, length);
    }

    /**
     * 生成 ECDSA P-256 密钥对
     * 返回与抖音兼容的格式：{ec_privateKey, ec_publicKey}
     */
    static generateKeyPair() {
        const { privateKey, publicKey } = crypto.generateKeyPairSync('ec', {
            namedCurve: 'prime256v1',
            publicKeyEncoding: {
                type: 'spki',
                format: 'pem'
            },
            privateKeyEncoding: {
                type: 'pkcs8',
                format: 'pem'
            }
        });
        
        return {
            ec_privateKey: privateKey,
            ec_publicKey: publicKey,
            timestamp: Date.now(),
            algorithm: "ECDSA",
            curve: "P-256"
        };
    }

    /**
     * 从 PEM 格式公钥提取原始公钥点 (65字节)
     */
    static extractRawPublicKey(pemPublicKey) {
        const base64Data = pemPublicKey
            .replace(/-----BEGIN PUBLIC KEY-----/, '')
            .replace(/-----END PUBLIC KEY-----/, '')
            .replace(/\s+/g, '');

        const spkiBuffer = Buffer.from(base64Data, 'base64');
        const rawPublicKey = spkiBuffer.slice(-65);
        return rawPublicKey.toString('base64');
    }

    /**
     * ECDH 密钥交换 + HKDF 密钥派生
     * 完全按照抖音 req.js 的实现
     */
    static performECDH(clientPrivateKeyPem, serverPublicKeyBase64) {
        try {
            const clientPrivateKey = crypto.createPrivateKey(clientPrivateKeyPem);
            const serverPublicKeyBuffer = Buffer.from(serverPublicKeyBase64, 'base64');

            const spkiHeader = Buffer.from([
                0x30, 0x59,
                0x30, 0x13,
                0x06, 0x07, 0x2a, 0x86, 0x48, 0xce, 0x3d, 0x02, 0x01,
                0x06, 0x08, 0x2a, 0x86, 0x48, 0xce, 0x3d, 0x03, 0x01, 0x07,
                0x03, 0x42, 0x00
            ]);

            const serverSpkiBuffer = Buffer.concat([spkiHeader, serverPublicKeyBuffer]);
            const serverPublicKeyPem = '-----BEGIN PUBLIC KEY-----\n' +
                                       serverSpkiBuffer.toString('base64').match(/.{1,64}/g).join('\n') +
                                       '\n-----END PUBLIC KEY-----';

            const serverPublicKey = crypto.createPublicKey(serverPublicKeyPem);

            const sharedSecret = crypto.diffieHellman({
                privateKey: clientPrivateKey,
                publicKey: serverPublicKey
            });

            // 对应 req.js 第 2077 行
            const derivedKey = this.hkdf(
                Buffer.alloc(0),
                sharedSecret,
                Buffer.alloc(0),
                32
            );

            return derivedKey;

        } catch (error) {
            throw error;
        }
    }

    /**
     * 按照抖音 req.js 的方式处理 path
     * 对应 req.js 第11139行和第11727-11733行的逻辑
     */
    static processPath(path, urlRewriteRules = []) {
        try {
            let processedPath = path;

            if (path && path.startsWith('http')) {
                try {
                    processedPath = new URL(path).pathname;
                } catch (e) {
                    processedPath = path;
                }
            }

            const defaultUrlRewriteRules = [
                ["/quick_login/v2", "/passport/sso/quick_login/v2/"],
                ["/check_qrconnect", "/passport/sso/check_qrconnect/"],
                ["/account_login/v2", "/passport/sso/account_login/v2/"],
                ["/one_login", "/passport/sso/one_login/"]
            ];

            const allRules = [...urlRewriteRules, ...defaultUrlRewriteRules];

            for (const [pattern, replacement] of allRules) {
                if (processedPath.includes(pattern)) {
                    processedPath = replacement;
                    break;
                }
            }

            if (processedPath && !processedPath.startsWith('/')) {
                processedPath = '/' + processedPath;
            }

            return processedPath;

        } catch (error) {
            return path;
        }
    }

    /**
     * 生成请求签名 (req_sign)
     * 完全按照抖音 req.js 的签名算法
     */
    static generateReqSign(clientPrivateKeyPem, serverPublicKeyBase64, ticket, path, timestamp, urlRewriteRules = []) {
        try {
            const derivedKey = this.performECDH(clientPrivateKeyPem, serverPublicKeyBase64);

            const timestampSeconds = typeof timestamp === 'number' && timestamp > 1e10
                ? Math.floor(timestamp / 1000)
                : timestamp;

            const processedPath = this.processPath(path, urlRewriteRules);

            // 对应 req.js 第 12226 行
            const signatureData = `ticket=${ticket}&path=${processedPath}&timestamp=${timestampSeconds}`;

            const hmac = crypto.createHmac('sha256', derivedKey);
            hmac.update(signatureData, 'utf8');
            const signature = hmac.digest();

            const reqSign = signature.toString('base64');

            return {
                req_sign: reqSign,
                sign_data: signatureData,
                req_content: "ticket,path,timestamp",
                timestamp: timestampSeconds,
                processed_path: processedPath
            };

        } catch (error) {
            throw error;
        }
    }

    /**
     * 生成完整的 Bd-Ticket-Guard-Client-Data
     * 返回抖音期望的完整数据格式
     */
    static generateBdTicketGuardClientData(clientPrivateKeyPem, clientPublicKeyPem, serverPublicKeyBase64, ticket, path, timestamp, tsSign = null, urlRewriteRules = []) {
        try {
            const reqSignData = this.generateReqSign(clientPrivateKeyPem, serverPublicKeyBase64, ticket, path, timestamp, urlRewriteRules);
            const clientPublicKeyBase64 = this.extractRawPublicKey(clientPublicKeyPem);

            const clientData = {
                ts_sign: tsSign || "ts.2.placeholder",
                req_content: "ticket,path,timestamp",
                req_sign: reqSignData.req_sign,
                timestamp: reqSignData.timestamp
            };

            const jsonString = JSON.stringify(clientData);
            const bdTicketGuardClientData = Buffer.from(jsonString, 'utf8').toString('base64');

            return {
                bdTicketGuardClientData: bdTicketGuardClientData,
                clientPublicKeyBase64: clientPublicKeyBase64,
                clientData: clientData,
                jsonString: jsonString,
                reqSignData: reqSignData,
                headers: {
                    'bd-ticket-guard-ree-public-key': clientPublicKeyBase64
                }
            };

        } catch (error) {
            throw error;
        }
    }
}

module.exports = DouyinKeyGenerator;
