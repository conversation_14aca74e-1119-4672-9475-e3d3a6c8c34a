const crypto = require('crypto');

/**
 * TikTok 密钥生成器
 * 基于对 TikTok req.js 的深度分析实现
 * 支持完整的 ECDH + HKDF + HMAC 签名流程
 */
class TikTokKeyGenerator {
    
    /**
     * HKDF (HMAC-based Key Derivation Function) 实现
     * 基于 RFC 5869 标准，与 TikTok req.js 中的实现保持一致
     */
    static hkdfExtract(salt, inputKeyMaterial) {
        if (salt.length === 0) {
            salt = Buffer.alloc(32, 0); // SHA-256 hash length
        }
        return crypto.createHmac('sha256', salt).update(inputKeyMaterial).digest();
    }

    static hkdfExpand(pseudoRandomKey, info, length) {
        const hashLength = 32; // SHA-256 hash length
        const n = Math.ceil(length / hashLength);
        let okm = Buffer.alloc(0);
        let t = Buffer.alloc(0);

        for (let i = 1; i <= n; i++) {
            const hmac = crypto.createHmac('sha256', pseudoRandomKey);
            hmac.update(t);
            hmac.update(info);
            hmac.update(Buffer.from([i]));
            t = hmac.digest();
            okm = Buffer.concat([okm, t]);
        }

        return okm.slice(0, length);
    }

    static hkdf(salt, inputKeyMaterial, info, length) {
        const prk = this.hkdfExtract(salt, inputKeyMaterial);
        return this.hkdfExpand(prk, info, length);
    }

    /**
     * 生成 ECDSA P-256 密钥对
     * 返回与 TikTok 兼容的格式：{ec_privateKey, ec_publicKey, ec_csr}
     */
    static generateKeyPair() {
        const { privateKey, publicKey } = crypto.generateKeyPairSync('ec', {
            namedCurve: 'prime256v1', // P-256
            publicKeyEncoding: {
                type: 'spki',
                format: 'pem'
            },
            privateKeyEncoding: {
                type: 'pkcs8',
                format: 'pem'
            }
        });
        
        return {
            ec_privateKey: privateKey,
            ec_publicKey: publicKey,
            ec_csr: this.generateCSR(publicKey),
            timestamp: Date.now(),
            algorithm: "ECDSA",
            curve: "P-256"
        };
    }

    /**
     * 生成证书签名请求 (CSR)
     */
    static generateCSR(publicKeyPem) {
        const timestamp = Date.now();
        const csrData = {
            publicKey: publicKeyPem.replace(/\s+/g, ''),
            timestamp: timestamp,
            subject: "CN=TikTok-Client"
        };
        return Buffer.from(JSON.stringify(csrData)).toString('base64');
    }

    /**
     * 从 PEM 格式公钥提取原始公钥点 (65字节)
     */
    static extractRawPublicKey(pemPublicKey) {
        const base64Data = pemPublicKey
            .replace(/-----BEGIN PUBLIC KEY-----/, '')
            .replace(/-----END PUBLIC KEY-----/, '')
            .replace(/\s+/g, '');
        
        const spkiBuffer = Buffer.from(base64Data, 'base64');
        // SPKI 格式：前面是 ASN.1 头部，最后 65 字节是原始公钥点
        const rawPublicKey = spkiBuffer.slice(-65);
        return rawPublicKey.toString('base64');
    }

    /**
     * ECDH 密钥交换 + HKDF 密钥派生
     * 完全按照 TikTok req.js 的实现
     */
    static performECDH(clientPrivateKeyPem, serverPublicKeyBase64) {
        try {
            // 1. 导入客户端私钥
            const clientPrivateKey = crypto.createPrivateKey(clientPrivateKeyPem);
            
            // 2. 构造服务器公钥的 SPKI 格式 (使用 PEM 格式)
            const serverPublicKeyBuffer = Buffer.from(serverPublicKeyBase64, 'base64');

            // SPKI 头部 (P-256 曲线)
            const spkiHeader = Buffer.from([
                0x30, 0x59, // SEQUENCE, 89 bytes
                0x30, 0x13, // SEQUENCE, 19 bytes
                0x06, 0x07, 0x2a, 0x86, 0x48, 0xce, 0x3d, 0x02, 0x01, // OID: ecPublicKey
                0x06, 0x08, 0x2a, 0x86, 0x48, 0xce, 0x3d, 0x03, 0x01, 0x07, // OID: prime256v1
                0x03, 0x42, 0x00 // BIT STRING, 66 bytes
            ]);

            const serverSpkiBuffer = Buffer.concat([spkiHeader, serverPublicKeyBuffer]);
            const serverPublicKeyPem = '-----BEGIN PUBLIC KEY-----\n' +
                                       serverSpkiBuffer.toString('base64').match(/.{1,64}/g).join('\n') +
                                       '\n-----END PUBLIC KEY-----';

            const serverPublicKey = crypto.createPublicKey(serverPublicKeyPem);
            
            // 3. 执行 ECDH
            const sharedSecret = crypto.diffieHellman({
                privateKey: clientPrivateKey,
                publicKey: serverPublicKey
            });

            // 4. 应用 HKDF (关键步骤)
            // 对应 req.js 第 2077 行：E.D)(new Uint8Array([]), new Uint8Array(a), new Uint8Array([]), 32)
            const derivedKey = this.hkdf(
                Buffer.alloc(0),    // salt: empty
                sharedSecret,       // input key material
                Buffer.alloc(0),    // info: empty  
                32                  // length: 32 bytes
            );

            return derivedKey;

        } catch (error) {
            throw error;
        }
    }

    /**
     * 按照 TikTok req.js 的方式处理 path
     * 对应 req.js 第11139行和第11727-11733行的逻辑
     */
    static processPath(path, urlRewriteRules = []) {
        try {
            // 1. URL 标准化 (对应 req.js 第11139行)
            let processedPath = path;

            // 如果是完整 URL，提取 pathname
            if (path && path.startsWith('http')) {
                try {
                    processedPath = new URL(path).pathname;
                } catch (e) {
                    processedPath = path;
                }
            }

            // 2. 应用 URL 重写规则 (对应 req.js 第13280-13281行的默认规则)
            const defaultUrlRewriteRules = [
                ["/quick_login/v2", "/passport/sso/quick_login/v2/"],
                ["/check_qrconnect", "/passport/sso/check_qrconnect/"],
                ["/account_login/v2", "/passport/sso/account_login/v2/"],
                ["/one_login", "/passport/sso/one_login/"]
            ];

            // 合并自定义规则和默认规则
            const allRules = [...urlRewriteRules, ...defaultUrlRewriteRules];

            // 应用重写规则 (对应 req.js 第11727行的逻辑: i.match(n) && (s = r))
            for (const [pattern, replacement] of allRules) {
                if (processedPath.includes(pattern)) {
                    processedPath = replacement;
                    break;
                }
            }

            // 3. 确保 path 格式正确
            // 如果没有前导斜杠，添加一个
            if (processedPath && !processedPath.startsWith('/')) {
                processedPath = '/' + processedPath;
            }

            return processedPath;

        } catch (error) {
            return path; // 失败时返回原始 path
        }
    }

    /**
     * 生成请求签名 (req_sign)
     * 完全按照 TikTok req.js 的签名算法
     */
    static generateReqSign(clientPrivateKeyPem, serverPublicKeyBase64, ticket, path, timestamp, urlRewriteRules = []) {
        try {
            // 1. 执行 ECDH + HKDF 获取派生密钥
            const derivedKey = this.performECDH(clientPrivateKeyPem, serverPublicKeyBase64);

            // 2. 确保时间戳是秒级 (对应 req.js 第 11719 行: Math.floor(new Date().getTime() / 1e3))
            const timestampSeconds = typeof timestamp === 'number' && timestamp > 1e10
                ? Math.floor(timestamp / 1000)  // 如果是毫秒级，转换为秒级
                : timestamp;  // 如果已经是秒级，直接使用

            // 3. 按照 TikTok 的方式处理 path (关键步骤)
            const processedPath = this.processPath(path, urlRewriteRules);

            // 4. 构造签名数据 (对应 req.js 第 12226 行)
            const signatureData = `ticket=${ticket}&path=${processedPath}&timestamp=${timestampSeconds}`;

            // 5. 使用派生密钥进行 HMAC-SHA256 签名
            const hmac = crypto.createHmac('sha256', derivedKey);
            hmac.update(signatureData, 'utf8');
            const signature = hmac.digest();

            // 6. 转换为 Base64
            const reqSign = signature.toString('base64');

            return {
                req_sign: reqSign,
                sign_data: signatureData,  // 实际的签名数据
                req_content: "ticket,path,timestamp",  // 只是字段描述
                timestamp: timestampSeconds,  // 使用秒级时间戳
                processed_path: processedPath  // 返回处理后的 path
            };

        } catch (error) {
            throw error;
        }
    }

    /**
     * 生成完整的 Bd-Ticket-Guard-Client-Data
     * 返回 TikTok 期望的完整数据格式
     */
    static generateBdTicketGuardClientData(clientPrivateKeyPem, clientPublicKeyPem, serverPublicKeyBase64, ticket, path, timestamp, tsSign = null, urlRewriteRules = []) {
        try {
            // 1. 生成请求签名 (使用 TikTok 的 path 处理方式)
            const reqSignData = this.generateReqSign(clientPrivateKeyPem, serverPublicKeyBase64, ticket, path, timestamp, urlRewriteRules);

            // 2. 提取客户端公钥 (用于请求头)
            const clientPublicKeyBase64 = this.extractRawPublicKey(clientPublicKeyPem);

            // 3. 构造客户端数据 (按照 TikTok 的真实格式)
            const clientData = {
                ts_sign: tsSign || "ts.2.placeholder",
                req_content: "ticket,path,timestamp",  // 字段描述
                req_sign: reqSignData.req_sign,
                timestamp: reqSignData.timestamp
            };

            // 4. 转换为 JSON 并编码为 Base64
            const jsonString = JSON.stringify(clientData);
            const bdTicketGuardClientData = Buffer.from(jsonString, 'utf8').toString('base64');

            return {
                bdTicketGuardClientData: bdTicketGuardClientData,
                clientPublicKeyBase64: clientPublicKeyBase64,
                clientData: clientData,
                jsonString: jsonString,
                reqSignData: reqSignData,  // 包含处理后的 path 信息
                headers: {
                    'bd-ticket-guard-ree-public-key': clientPublicKeyBase64
                }
            };

        } catch (error) {
            throw error;
        }
    }

    /**
     * 使用已知的密钥对生成数据 (用于测试)
     */
    static generateWithKnownKeyPair(keyPair, serverPublicKeyBase64, ticket, path, timestamp) {
        return this.generateBdTicketGuardClientData(
            keyPair.ec_privateKey,
            keyPair.ec_publicKey,
            serverPublicKeyBase64,
            ticket,
            path,
            timestamp
        );
    }
}

module.exports = TikTokKeyGenerator;
