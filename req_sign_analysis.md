# 抖音 req_sign 生成流程完整分析

## 🔍 从 req.js 中提取的完整流程

### 1. 签名数据构造 (第11731行)
```javascript
sign_data: "ticket=".concat(e, "&path=").concat(s, "&timestamp=").concat(a)
```

**格式**: `ticket={ticket}&path={path}&timestamp={timestamp}`

### 2. 主要签名函数调用链

#### A. 入口函数 `signWithKeysInfo` (第9073行)
```javascript
r.signWithKeysInfo = function(t) {
    // t.sign_data = "ticket=xxx&path=xxx&timestamp=xxx"
    // t.req_content = "ticket,path,timestamp"
    // t.timestamp = 时间戳
}
```

#### B. ECDH 密钥获取 (第9153行)
```javascript
[4, this.initECDHKey()]  // 如果没有缓存的 ECDH 密钥
```

#### C. HMAC 签名调用 (第9159行)
```javascript
[4, null === (P = this.cryptoSDK) || void 0 === P ? void 0 : P.signWithHmac(e || h, S)]
// e = sign_data (签名数据)
// S = ECDH 密钥
```

### 3. 核心签名函数 `signWithHmac` (第6300行)

```javascript
this.signWithHmac = function(t, r) {
    // t = 签名数据 (sign_data)
    // r = ECDH 密钥
    
    o = eZ(r, t),           // 第6318行: HMAC 计算
    i = (0, C.MU)(o),       // 第6320行: 转换为 Base64
}
```

### 4. HMAC 实现 (第6278行)
```javascript
eZ = ez.sha256.hmac
```

**关键发现**: `eZ(r, t)` = `HMAC-SHA256(key=r, data=t)`

### 5. ECDH 密钥派生流程 (第2070-2077行)

```javascript
// 1. ECDH 原始密钥交换
[4, crypto.subtle.deriveBits({
    name: "ECDH",
    public: i
}, n, 256)]

// 2. HKDF 密钥派生 (关键步骤!)
[4, (0, E.D)(new Uint8Array([]), new Uint8Array(a), new Uint8Array([]), 32)]
```

## 🔄 完整流程总结

### 抖音的实际流程:
1. **构造签名数据**: `"ticket={ticket}&path={path}&timestamp={timestamp}"`
2. **ECDH 密钥交换**: 客户端私钥 + 服务器公钥 → 原始共享密钥 (32字节)
3. **HKDF 密钥派生**: `HKDF(salt=[], ikm=原始共享密钥, info=[], length=32)` → 最终密钥 (32字节)
4. **HMAC 签名**: `HMAC-SHA256(key=最终密钥, data=签名数据)` → 原始签名 (32字节)
5. **Base64 编码**: 原始签名 → req_sign

### 关键参数顺序 ⚠️
**重要发现**: HMAC 调用是 `eZ(r, t)` 其中:
- `r` = ECDH 密钥 (key)
- `t` = 签名数据 (data)

即: `HMAC-SHA256(key=ECDH密钥, data=签名数据)`

## 🆚 与我们实现的对比

### 我们当前的实现:
```javascript
// 1. 签名数据构造 ✅
const signData = `ticket=${ticket}&path=${path}&timestamp=${timestamp}`;

// 2. ECDH + HKDF ✅  
const sharedSecret = crypto.diffieHellman({...});
const finalKey = HKDF.derive(salt, sharedSecret, info, 32);

// 3. HMAC 签名 ❓
const hmac = crypto.createHmac('sha256', finalKey);
hmac.update(signData);
return hmac.digest('base64');
```

### 可能的问题点:

#### 1. **HMAC 参数顺序**
- 抖音: `HMAC-SHA256(key=ECDH密钥, data=签名数据)`
- 我们: `HMAC-SHA256(key=ECDH密钥, data=签名数据)` ✅

#### 2. **签名数据编码**
- 需要确认签名数据是否需要特定编码 (UTF-8, etc.)

#### 3. **ECDH 密钥格式**
- 需要确认 ECDH 密钥的具体格式和字节序

#### 4. **时间戳格式**
- 抖音使用: `Math.floor(new Date().getTime() / 1e3)` (秒级时间戳)
- 我们使用: `Math.floor(Date.now() / 1000)` ✅

## 🔧 下一步调试方向

1. **验证 ECDH 密钥**: 确保我们生成的 ECDH 密钥与抖音完全一致
2. **验证 HKDF 实现**: 确保 HKDF 参数和实现与抖音一致
3. **验证签名数据**: 确保字符串编码和格式完全匹配
4. **验证 HMAC 实现**: 确保 HMAC 计算方式完全一致

## 📝 测试建议

创建一个对比测试，使用相同的:
- 客户端私钥
- 服务器公钥  
- ticket
- path
- timestamp

然后逐步验证每个步骤的输出是否与抖音一致。
