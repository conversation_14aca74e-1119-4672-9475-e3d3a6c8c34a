#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from douyin_key_generator import DouyinKeyGenerator
import json
import base64

def test_clean_version():
    """测试抖音密钥生成器 - 使用真实数据"""
    
    # 真实密钥对
    real_key_pair = {
****************************************************************************************************************************************************************************************************************************************************************************
        "ec_publicKey": "-----BEGIN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEBidm8e+nQbS3mFAHv/3D7928s6Wjdwt4AVo4CKiU2dz7M5VHCkC3IMbkdZNJ9tf7S3OnCQghl1d5sjMat5HxFA==\n-----END PUBLIC KEY-----"
    }
    
    # 抖音服务器公钥
    server_public_key_base64 = 'BITaWUM+V9zxGr7knkdeGDh50WM+cIXdRrskGiIT9+g/2bsvkQshy/EKATFpEIdtDyQIIr8CtngVf+oKhtkCROg='
    
    # 真实测试数据
    real_data = {
        "ticket": "hash.lIyPEwMuuN78g2DGuW0hMK4jyPxpv5LN0Ak+vOJUZY=",
        "ts_sign": "ts.2.f85fa14375fff92cdd5ea6c8f793991858719360bcd893759a141ec72e687dc1c4fbe87d2319cf05318624ceda14911ca406dedbebeddb2e30fce8d4fa02575d",
        "client_cert": "pub.BAYnZvHvp0G0t5hQB7/9w+/dvLOlo3cLeAFaOAiolNnc+zOVRwpAtyDG5HWTSfbX+0tzpwkIIZdXebIzGreR8RQ=",
        "log_id": "20250801143535858164FF9A5A62A0FD53",
        "create_time": 1754030405
    }
    
    # 测试参数
    ticket = real_data["ticket"]
    path = 'aweme/v1/web/comment/publish'
    timestamp = real_data["create_time"]
    ts_sign = real_data["ts_sign"]
    
    print("测试抖音密钥生成器")
    print(f"Ticket: {ticket}")
    print(f"Path: {path}")
    print(f"Timestamp: {timestamp}")
    print()
    
    try:
        # 1. 验证公钥提取
        print("1. 验证公钥提取")
        extracted_public_key = DouyinKeyGenerator.extract_raw_public_key(real_key_pair["ec_publicKey"])
        expected_public_key = 'BAYnZvHvp0G0t5hQB7/9w+/dvLOlo3cLeAFaOAiolNnc+zOVRwpAtyDG5HWTSfbX+0tzpwkIIZdXebIzGreR8RQ='
        
        print(f"提取的公钥: {extracted_public_key}")
        print(f"期望的公钥: {expected_public_key}")
        print(f"公钥匹配: {'是' if extracted_public_key == expected_public_key else '否'}")
        print()
        
        # 2. 生成 Bd-Ticket-Guard-Client-Data
        print("2. 生成 Bd-Ticket-Guard-Client-Data")
        result = DouyinKeyGenerator.generate_bd_ticket_guard_client_data(
            real_key_pair["ec_privateKey"],
            real_key_pair["ec_publicKey"],
            server_public_key_base64,
            ticket,
            path,
            timestamp,
            ts_sign
        )
        
        print(f"Bd-Ticket-Guard-Client-Data: {result['bdTicketGuardClientData']}")
        print(f"客户端公钥: {result['clientPublicKeyBase64']}")
        print()
        
        # 3. 验证数据完整性
        print("3. 验证数据完整性")
        decoded = json.loads(base64.b64decode(result['bdTicketGuardClientData']).decode('utf-8'))
        required_fields = ['ts_sign', 'req_content', 'req_sign', 'timestamp']
        has_all_fields = all(field in decoded for field in required_fields)
        
        print(f"必需字段: {', '.join(required_fields)}")
        print(f"实际字段: {', '.join(decoded.keys())}")
        print(f"字段完整: {'是' if has_all_fields else '否'}")
        print()
        
        # 4. 输出使用说明
        print("4. 使用说明")
        print("请求头:")
        print(f'  "bd-ticket-guard-ree-public-key": "{result["clientPublicKeyBase64"]}"')
        print("请求体参数:")
        print(f'  "Bd-Ticket-Guard-Client-Data": "{result["bdTicketGuardClientData"]}"')
        print()
        
        # 5. 生成新密钥对测试
        print("5. 测试生成新密钥对")
        new_key_pair = DouyinKeyGenerator.generate_key_pair()
        print(f"算法: {new_key_pair['algorithm']}")
        print(f"曲线: {new_key_pair['curve']}")
        print(f"时间戳: {new_key_pair['timestamp']}")
        print()
        
        print("所有测试完成!")
        return result
        
    except Exception as error:
        print(f"测试失败: {str(error)}")
        return None

if __name__ == "__main__":
    test_clean_version()
