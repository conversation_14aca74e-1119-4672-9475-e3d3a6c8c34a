#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess
import json
import base64

class DouyinKeyGenerator:
    """Python 调用 Node.js 版本的抖音密钥生成器"""
    
    @staticmethod
    def _call_node(method, *args):
        """调用 Node.js 方法"""
        cmd = [
            'node', '-e', 
            f"""
            const TikTokKeyGenerator = require('./node-key-generator-clean');
            const args = {json.dumps(list(args))};
            try {{
                let result;
                switch ('{method}') {{
                    case 'extractRawPublicKey':
                        result = TikTokKeyGenerator.extractRawPublicKey(args[0]);
                        break;
                    case 'generateBdTicketGuardClientData':
                        result = TikTokKeyGenerator.generateBdTicketGuardClientData(...args);
                        break;
                    case 'generateKeyPair':
                        result = TikTokKeyGenerator.generateKeyPair();
                        break;
                    default:
                        throw new Error('Unknown method: {method}');
                }}
                console.log(JSON.stringify(result));
            }} catch (error) {{
                console.error(JSON.stringify({{error: error.message}}));
                process.exit(1);
            }}
            """
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode != 0:
            raise Exception(f"Node.js 调用失败: {result.stderr}")
        
        return json.loads(result.stdout.strip())
    
    @staticmethod
    def extract_raw_public_key(pem_public_key):
        """提取原始公钥"""
        return DouyinKeyGenerator._call_node('extractRawPublicKey', pem_public_key)
    
    @staticmethod
    def generate_bd_ticket_guard_client_data(client_private_key, client_public_key, 
                                           server_public_key_base64, ticket, path, 
                                           timestamp, ts_sign=None):
        """生成 Bd-Ticket-Guard-Client-Data"""
        return DouyinKeyGenerator._call_node('generateBdTicketGuardClientData',
                                           client_private_key, client_public_key,
                                           server_public_key_base64, ticket, path,
                                           timestamp, ts_sign)
    
    @staticmethod
    def generate_key_pair():
        """生成新密钥对"""
        return DouyinKeyGenerator._call_node('generateKeyPair')

def generate_client_data():
    """生成抖音客户端数据"""

    key_pair = {
****************************************************************************************************************************************************************************************************************************************************************************
        "ec_publicKey": "-----BEGIN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEBidm8e+nQbS3mFAHv/3D7928s6Wjdwt4AVo4CKiU2dz7M5VHCkC3IMbkdZNJ9tf7S3OnCQghl1d5sjMat5HxFA==\n-----END PUBLIC KEY-----"
    }

    server_public_key_base64 = 'BITaWUM+V9zxGr7knkdeGDh50WM+cIXdRrskGiIT9+g/2bsvkQshy/EKATFpEIdtDyQIIr8CtngVf+oKhtkCROg='

    request_data = {
        "ticket": "hash.lIyPEwMuuN78g2DGuW0hMK4jyPxpv5LN0Ak+vOJUZY=",
        "ts_sign": "ts.2.f85fa14375fff92cdd5ea6c8f793991858719360bcd893759a141ec72e687dc1c4fbe87d2319cf05318624ceda14911ca406dedbebeddb2e30fce8d4fa02575d",
        "log_id": "20250801143535858164FF9A5A62A0FD53",
        "create_time": 1754030405
    }

    ticket = request_data["ticket"]
    path = 'aweme/v1/web/comment/publish'
    timestamp = request_data["create_time"]
    ts_sign = request_data["ts_sign"]

    print("抖音密钥生成器")
    print(f"Ticket: {ticket}")
    print(f"Path: {path}")
    print(f"Timestamp: {timestamp}")
    print()

    try:
        print("客户端私钥证书:")
        print(key_pair["ec_privateKey"])
        print()

        print("客户端公钥证书:")
        print(key_pair["ec_publicKey"])
        print()

        print("服务器公钥 (Base64):")
        print(server_public_key_base64)
        print()

        extracted_public_key = DouyinKeyGenerator.extract_raw_public_key(key_pair["ec_publicKey"])

        result = DouyinKeyGenerator.generate_bd_ticket_guard_client_data(
            key_pair["ec_privateKey"],
            key_pair["ec_publicKey"],
            server_public_key_base64,
            ticket,
            path,
            timestamp,
            ts_sign
        )

        print("Bd-Ticket-Guard-Client-Data:")
        print(result['bdTicketGuardClientData'])
        print()

        print("客户端公钥 (Raw Base64):")
        print(result['clientPublicKeyBase64'])
        print()

        decoded = json.loads(base64.b64decode(result['bdTicketGuardClientData']).decode('utf-8'))
        print("客户端数据结构:")
        print(json.dumps(decoded, indent=2, ensure_ascii=False))
        print()

        print("使用说明:")
        print("请求头:")
        print(f'  "bd-ticket-guard-ree-public-key": "{result["clientPublicKeyBase64"]}"')
        print("请求体参数:")
        print(f'  "Bd-Ticket-Guard-Client-Data": "{result["bdTicketGuardClientData"]}"')
        print()

        new_key_pair = DouyinKeyGenerator.generate_key_pair()
        print("新生成的密钥对:")
        print(f"算法: {new_key_pair['algorithm']}")
        print(f"曲线: {new_key_pair['curve']}")
        print(f"时间戳: {new_key_pair['timestamp']}")
        print()

        return result

    except Exception as error:
        print(f"错误: {str(error)}")
        return None

if __name__ == "__main__":
    generate_client_data()
